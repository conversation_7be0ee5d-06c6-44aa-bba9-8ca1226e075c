// Copyright Isto Inc.

using Isto.Core.Audio;
using Isto.Core.Data;
using Isto.Core.Game;
using Isto.Core.Localization;
using Isto.Core.Scenes;
using Isto.GTW.Audio;
using Isto.GTW.Managers;
using System;
using System.Collections;
using System.Collections.Generic;
using Isto.GTW.SectionLoading;
using PixelCrushers.DialogueSystem;
using UnityEngine;
using Zenject;
using Isto.GTW.Configuration;
using Isto.GTW.Providers;
using UnityEngine.SceneManagement;

namespace Isto.GTW
{
    public class GTWGameState : GameState
    {
        // ENUM

        public enum SaveSlotId
        {
            MAIN_GAME = 0,
            DOINKLER_PORTFOLIO_WORLD_1 = 1,
            DOINKLER_PORTFOLIO_WORLD_2 = 2,
            DOINKLER_PORTFOLIO_WORLD_3 = 3,
            DOINKLER_PORTFOLIO_WORLD_4 = 4,
            DOINKLER_PORTFOLIO_WORLD_5 = 5,
            DOINKLER_PORTFOLIO_WORLD_6 = 6,
            DOINKLER_SPECIAL = 7, // Save slots 1-6 are reserved for Doinkler Portfolio
        }

        // UNITY HOOKUP
        [SerializeField][SceneReference] private string _doinklerEssentialScene;
        [SerializeField] private GTWDoinklerPortfolioDefinition _doinklerPortfolioDefinition;


        // PROPERTIES

        public TimeManager TimeManager { get; set; }
        public bool StartedWithoutGameMode { get; set; }

        public GTWGameLevelDefinition GameLevelDefinition { get; set; }
        public bool IsDoinklerPortfolio => GameLevelDefinition != null;

        public int SaveSlot => _saveSlot;



        // INJECTION

        private GTWGameSounds _sounds;
        private IDoinklerWorldDefinitionProvider _doinklerWorldDefinitionProvider;


        [Inject]
        private void Inject(IGameSounds sounds, IDoinklerWorldDefinitionProvider doinklerWorldDefinitionProvider)
        {
            _sounds = sounds as GTWGameSounds;
            _doinklerWorldDefinitionProvider = doinklerWorldDefinitionProvider;
        }


        // OTHER METHODS

        public void SetSaveSlot(int saveSlot)
        {
            _saveSlot = saveSlot;
        }

        public override GameStateSaveData SaveGameStateData()
        {
            GameStateSaveData data = new GameStateSaveData();
            data.isAutoSave = false;
            data.saveSlotName = $"{Loc.Get(defaultSlotNameKey)}{_saveSlot}";
            if (_selectedSaveSlotMetaData != null)
                data.saveSlotName = _selectedSaveSlotMetaData.saveSlotName;
            if (CurrentGameMode != null)
                data.gameModeName = CurrentGameMode.InternalName;
            else
                Debug.LogWarning("Saving Game State: no game mode configured, it will be missing in the save file.");
            data.gameVersion = version.ActiveVersion.VersionText;
            data.internalBuildVersion = version.ActiveVersion.shortDate;
            if (TimeManager != null)
            {
                data.totalGameTimeSeconds = TimeManager.TotalGameTimeUnscaled;
            }
            data.saveDate = DateTime.Now;
            data.autoSaveDates = new List<DateTime>();
            data.themeData = new ThemeManagerData();
            return data;
        }

        private float _finalizationProgress = 1f;
        protected override IEnumerator FinalizeLoading()
        {
            _finalizationProgress = 0f;
            yield return null;
            _finalizationProgress = 0.1f;

            // Wait until all section scenes around the player are loaded.
            while (SectionLoader.IsBusy)
            {
                _finalizationProgress = 0.1f + SectionLoader.SectionLoadingProgress * 0.7f;

                yield return null;
            }

            _finalizationProgress = 0.8f;

            // Wait the character's IK to settle.
            float timer = 1f;
            while (timer > 0f)
            {
                float localProgressNormalized = 1f - timer;
                _finalizationProgress = 0.8f + (localProgressNormalized * 0.2f);
                yield return null;
                timer -= Time.deltaTime;
            }

            _finalizationProgress = 1f;

            DialogueManager.instance.Unpause();
            _sounds.StopAllEventInstances();
            _sounds.SetGameplaySoundsPaused(false);

            yield return null;
        }

        protected override float GetFinalizeProgress()
        {
            return _finalizationProgress;
        }

        protected override bool IsEssentialScene(Scene newScene)
        {
            bool isEssentialScene;
            if (IsDoinklerPortfolio)
            {
                isEssentialScene = _currentEssentialScene == _doinklerEssentialScene;
            }
            else
            {
                isEssentialScene = base.IsEssentialScene(newScene);
            }

            return isEssentialScene;
        }

        public int GetCurrentDoinklerWorld()
        {
            int currentDoinklerLevel = -1;

            if (IsDoinklerPortfolio)
            {
                currentDoinklerLevel = _doinklerPortfolioDefinition.GTWGameWorldDefinitions.IndexOf(_doinklerWorldDefinitionProvider.Current);
            }

            return currentDoinklerLevel;
        }

        public int GetDoinklerWorldCount()
        {
            int currentDoinklerWorldCount = -1;

            if (IsDoinklerPortfolio)
            {
                currentDoinklerWorldCount = _doinklerPortfolioDefinition.GTWGameWorldDefinitions.Count;
            }

            return currentDoinklerWorldCount;
        }
    }
}