using System;
using System.Collections.Generic;
using System.Xml.Serialization;
using UnityEngine;
using UnityEngine.Serialization;

namespace Isto.GTW.Data
{
    [Serializable, XmlRoot("PlayerData")]
    public class PlayerSaveData
    {
        public Vector3 position;
        public Vector3 forward;
        public Vector3 velocity;
        public Vector3 angularVelocity;

        [FormerlySerializedAs("checkpoingId")]
        public int checkpointId = -1;

        [FormerlySerializedAs("seenCheckoints")]
        public List<int> seenCheckpoints;

        public float totalGameTime;
        public float totalGameTimeUnscaled;

        public float totalHeightGained;
        public float totalDistanceFallen;

        public int totalBigCrashes;
        public int dunceHatsCollected;

        public bool giveUpAbility = false;

        public bool ContentEquals(PlayerSaveData other)
        {
            if (other == null)
                return false;
            if (this.position != other.position)
                return false;
            if (this.forward != other.forward)
                return false;
            if (this.velocity != other.velocity)
                return false;
            if (this.angularVelocity != other.angularVelocity)
                return false;
            if (this.totalGameTime != other.totalGameTime)
                return false;
            if (this.totalGameTimeUnscaled != other.totalGameTimeUnscaled)
                return false;
            if (this.totalHeightGained != other.totalHeightGained)
                return false;
            if (this.totalDistanceFallen != other.totalDistanceFallen)
                return false;
            if (this.totalBigCrashes != other.totalBigCrashes)
                return false;
            if (this.dunceHatsCollected != other.dunceHatsCollected)
                return false;
            if (this.giveUpAbility != other.giveUpAbility)
                return false;

            return true;
        }
    }
}