// Copyright Isto Inc.
using System;
using System.Collections;
using UnityEngine;
using DG.Tweening;
using Isto.GTW.Audio;
using Isto.GTW.Player;
using Zenject;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Isto.GTW.LevelFeatures
{
    public class MovingObject : MonoBehaviour
    {
        [System.Serializable]
        public class MoveStep
        {
            public Transform targetTransform = null;

            [Space]
            [Tooltip("Duration to reach this position.")]
            public float moveTime = 1f;
            public Ease easeType = Ease.Linear;
            [Tooltip("Hold this position for this amount of time.")]
            public float delay = 0f;

            public bool forceHoldGrab = false;
            public bool forceDeactivateGrab = false;

            public Vector3 TargetPosition => targetTransform.position;
            public Vector3 TargetRotation => targetTransform.rotation.eulerAngles;
        }

        public enum LoopType {LOOP, STOP_AT_FIRST, STOP_AT_LAST}

        // UNITY HOOKUP

        public Rigidbody objectToMove;
        public bool startOnAwake = true;
        public LoopType loop = LoopType.LOOP;

        [Space]
        public bool exitLaunch = false;
        public Transform launchDirection = null;
        public float launchVelocity = 20f;
        public MoveStep[] moveSteps = new MoveStep[0];

        [Space]
        [Header("Sound Effect")]
        [SerializeField] private ObstacleAudioOneShot _obstacleAudioOneShot;


        // OTHER FIELDS

        private bool _isExecuting = false;
        private int _currentPositionIndex = 0;
        private Sequence _sequenceTween = null;

        private Collider[] _colliders = new Collider[0];


        // PROPERTIES

        public bool IsPlaying => _sequenceTween != null && _sequenceTween.IsPlaying();

        public bool IsExecuting
        {
            get => _isExecuting;
            set => _isExecuting = value;
        }


        // EVENTS

        public System.Action<int> OnMoveStep;


        // INJECTION

        private PlayerController _playerController;

        [Inject]
        public void Inject(PlayerController playerController)
        {
            _playerController = playerController;
        }


        // LIFECYCLE EVENTS

        private void Awake()
        {
            _playerController.OnActivateGrab += PlayerController_OnActivateGrab;
            _playerController.OnDeactivateGrab += PlayerController_OnDeactivateGrab;

            objectToMove.isKinematic = true;
            objectToMove.useGravity = false;
            objectToMove.collisionDetectionMode = CollisionDetectionMode.ContinuousSpeculative;

            _colliders = GetComponentsInChildren<Collider>();

            InitializeToFirstPosition();

            if (startOnAwake)
            {
                Play();
            }
        }

        public void OnDestroy()
        {
            _playerController.OnActivateGrab -= PlayerController_OnActivateGrab;
            _playerController.OnDeactivateGrab -= PlayerController_OnDeactivateGrab;

            if (_sequenceTween != null)
            {
                _sequenceTween.Kill();
                _sequenceTween = null;
            }
        }

        private void OnValidate()
        {
            if (objectToMove != null)
            {
                objectToMove.isKinematic = true;
                objectToMove.useGravity = false;
                objectToMove.collisionDetectionMode = CollisionDetectionMode.ContinuousSpeculative;

                #if UNITY_EDITOR
                EditorUtility.SetDirty(objectToMove.gameObject);
                #endif
            }
        }


        // EVENT HANDLING

        private Vector3 _currentBodyDirection = Vector3.zero;
        public bool IsGrabbingMovingObject => _playerController.GrabLinkParentRigidbody == objectToMove;

        private void PlayerController_OnActivateGrab()
        {
            if (moveSteps[_currentPositionIndex].forceHoldGrab && IsGrabbingMovingObject)
            {
                _playerController.ForceHoldGrab = true;
            }
        }

        private void PlayerController_OnDeactivateGrab()
        {
            if (exitLaunch && moveSteps[_currentPositionIndex].forceDeactivateGrab && _playerController.GrabLinkParentRigidbody == objectToMove)
            {
                StartCoroutine(DisableColliders(0.5f));

                Vector3 direction = _currentBodyDirection;
                if (_playerController.Velocity.sqrMagnitude > 0.001f)
                {
                    direction = _playerController.Velocity.normalized;
                }
                _playerController.SetVelocity(direction * launchVelocity, Vector3.zero);
            }
        }


        // OTHER METHODS

        public void InitializeToFirstPosition()
        {
            MoveStep moveStep = this.moveSteps[0];
            objectToMove.transform.position = moveStep.TargetPosition;
            objectToMove.transform.rotation = Quaternion.Euler(moveStep.TargetRotation);
        }

        public void Play()
        {
            if (_obstacleAudioOneShot != null && !_isExecuting && !IsPlaying)
            {
                _obstacleAudioOneShot.PlaySound();
            }

            if (_isExecuting || IsPlaying)
            {
                return;
            }

            _isExecuting = true;
            MoveToNextPoint();
        }

        public void Stop()
        {
            _isExecuting = false;
        }

        public void MoveToNextPoint()
        {
            if (this.moveSteps.Length == 0)
            {
                Debug.LogError("_movePositions is empty!", this.gameObject);
                return;
            }

            if (!_isExecuting)
            {
                return;
            }

            if (IsPlaying)
            {
                return;
            }

            if (_currentPositionIndex == (this.moveSteps.Length - 1) && loop == LoopType.STOP_AT_LAST)
            {
                _isExecuting = false;
                return;
            }

            _currentPositionIndex++;
            _currentPositionIndex %= this.moveSteps.Length;//modulus to wrap around the length of the list

            MoveStep moveStep = this.moveSteps[_currentPositionIndex];
            if (_sequenceTween != null)
            {
                _sequenceTween.Kill();
                _sequenceTween = null;
            }
            _sequenceTween = DOTween.Sequence();

            if (_currentPositionIndex == 0 && loop == LoopType.STOP_AT_FIRST)
            {
                _sequenceTween = CreateSequence(moveStep);

                _isExecuting = false;
            }
            else
            {
                _sequenceTween = CreateSequence(moveStep);
                _sequenceTween.OnComplete(MoveToNextPoint);
            }
        }

        public Sequence CreateSequence(MoveStep moveStep)
        {
            return DOTween.Sequence().Join(objectToMove.DOMove(moveStep.TargetPosition, moveStep.moveTime)
                    .SetEase(moveStep.easeType))
                .Join(objectToMove.DORotate(moveStep.TargetRotation, moveStep.moveTime)
                    .SetEase(moveStep.easeType))
                .AppendCallback(MoveStepFinished)
                .AppendInterval(moveStep.delay);
        }

        private void MoveStepFinished()
        {
            if (OnMoveStep != null)
            {
                OnMoveStep(_currentPositionIndex);
            }

            if (_playerController.IsGrabLinkActive && moveSteps[_currentPositionIndex].forceDeactivateGrab && (_playerController.GrabLinkParentRigidbody == objectToMove || _playerController.GrabLinkParentRigidbody.transform.IsChildOf(objectToMove.transform)))
            {
                bool playerBehind = false;
                if (launchDirection != null)
                {
                    Plane plane = new Plane(launchDirection.forward, _playerController.PlayerModelController.transform.position);
                    float distanceFromPlane = plane.GetDistanceToPoint(_playerController.Rigidbody.position);
                    if (distanceFromPlane < 0f)
                    {
                        // A desync has happened between the Rigidbody position and PlayerModel position due to a desync
                        // between the DeltaTime and FixedTime.
                        playerBehind = true;
                    }
                }

                _playerController.ForceHoldGrab = false;
                
                _currentBodyDirection = _playerController.Direction;
                if (launchDirection != null && playerBehind)
                {
                    // Fix the desync by using a provided fixed launch direction and teleporting the Rigidbody back to
                    // the last known PlayerModel position.
                    _currentBodyDirection = launchDirection.forward;
                    
                    _playerController.TeleportCharacter(_playerController.PlayerModelController.transform.position);
                }

                _playerController.DeactivateGrabLink(0.5f);
                if (exitLaunch)
                {
                    _playerController.SetVelocity(_currentBodyDirection * launchVelocity, Vector3.zero);
                }
            }
        }

        private IEnumerator DisableColliders(float duration)
        {
            foreach (Collider col in _colliders)
            {
                col.enabled = false;
            }

            yield return new WaitForSeconds(duration);

            foreach (Collider col in _colliders)
            {
                col.enabled = true;
            }
        }
    }

}