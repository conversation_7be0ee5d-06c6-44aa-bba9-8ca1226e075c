using Isto.Core;
using Isto.Core.Data;
using System.Collections;
using UnityEngine;
using Zenject;

namespace Isto.GTW.LevelFeatures.LevelEvents
{
    public class SaveDataEvent : LevelEvent
    {
        private IGameData _gameData;
        private GTWGameState _gameState;



        [Inject]
        public void Inject(IGameData gameData, GTWGameState gameState)
        {
            _gameData = gameData;
            _gameState = gameState;
        }
        
        public override void TriggerEvent(bool initialization = false)
        {
            StartCoroutine(AutoSave());
        }
        
        private IEnumerator AutoSave()
        {
            Debug.Log("ActivateCheckpointEvent.AutoSave()");

            Events.RaiseEvent(Events.GAME_AUTOSAVING); // For indicator

            int slotNumber = _gameState.SaveSlot;

            _gameState.SelectSaveGame(slotNumber);
            _gameState.LoadGameStateData(null, -1);

            _gameData.SaveGameData(slotNumber, false, success =>
            {
                if (success)
                {
                    Events.RaiseEvent(Events.GAME_SAVED);

                    // TODO: I don't think we need this
                    _gameState.LoadSaveSlotMetaData(slotNumber, null);
                }
                else
                {
                    // TODO?
                    // The internal cause of the error should be causing an error dialog popup to happen
                    Debug.LogError($"SaveGameData for slot#{slotNumber} has failed ");
                }
            });
            
            yield return null;
        }
    }
}