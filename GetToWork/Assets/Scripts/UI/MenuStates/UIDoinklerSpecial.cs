// Copyright Isto Inc.

using Isto.Core;
using Isto.Core.Data;
using Isto.Core.Inputs;
using Isto.Core.Speedrun;
using Isto.Core.StateMachine;
using Isto.Core.UI;
using Isto.GTW.Configuration;
using Isto.GTW.Providers;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using Zenject;

namespace Isto.GTW.UI
{
    /// <summary>
    /// NOTE FROM STEPHEN - 2025-06-03 
    /// I copied this script and modified it from UISetGameModeSubstate. Feel free to delete anything redundant
    /// </summary>
    public class UIDoinklerSpecial : CoreUISetGameModeSubState
    {
        // UNITY HOOKUP

        [Header("Confirm Dialogue")]
        [SerializeField] private UISimpleConfirmModalState _confirmationSubState;

        [Header("Leaderboard")]
        [SerializeField] private Transform _leaderboardContainer;
        [SerializeField] private GameObject _leaderboardEntryPrefab;


        [Header("Progress Report")]
        [SerializeField] private TextMeshProUGUI _employeeNameText;
        [SerializeField] private TextMeshProUGUI _totalTimeText;
        [SerializeField] private TextMeshProUGUI _currentCheckpointText;
        [SerializeField] private CoreButton _doinklerStartButton;

        [Header("Bottom of Folder")]
        [SerializeField] private CoreButton _freshStartButton;
        
        [Space]
        [SerializeField] private GTWGameLevelDefinition _gameLevelDefinition = null;


        // OTHER FIELDS

        private GTWGameLevelDefinition _selectedLevel = null;
        private MonoPushdownStateMachine _menuController;
        private Selectable _currentSelection;


        // PROPERTIES


        // INJECTION

        private DiContainer _container;
        private GTWGameState _gameState;
        private IGameData _gameData;


        [Inject]
        public void Inject(DiContainer container, GTWGameState gamestate, IGameData gameData)
        {
            _container = container;
            _gameState = gamestate;
            _gameData = gameData;
        }


        // LIFECYCLE METHODS

        public override void Enter(MonoStateMachine controller)
        {
            base.Enter(controller);

            SetupStateMachineController(controller);

            // Subscribe to input mode changes to update navigation when switching between mouse and controller
            Events.Subscribe(Events.INPUT_MODE_CHANGED, Events_OnInputModeChanged);

            SetupProgressReport();
            SetupCompanyLeaderboard();
        }

        public override void Exit(MonoStateMachine controller)
        {
            base.Exit(controller);

            // Unsubscribe from events
            Events.UnSubscribe(Events.INPUT_MODE_CHANGED, Events_OnInputModeChanged);
        }

        public override MonoState Run(MonoStateMachine controller)
        {
            if (_controls.GetButton(UserActions.UICANCEL) &&
                _doinklerStartButton.IsSelected &&
                _currentSelection != null)
            {
                _currentSelection.Select();
            }
            else
            {
                if (_controls.GetButtonDown(UserActions.UICANCEL))
                {
                    _menuController.ExitSubState();
                }
            }

            // If using controller, ensure we always have a highlighted item
            if (_controls.UsingJoystick() && EventSystem.current.currentSelectedGameObject == null)
            {
                HighlightFallbackSelectable();
            }

            return this;
        }



        public override void ReturnFromSubState(MonoStateMachine controller, MonoState previousState)
        {
            // When returning from a sub-state (like the confirmation dialog), ensure navigation is set up correctly
            if (_controls.UsingJoystick())
            {
                // Try to restore the previous highlight if there was one
                if (EventSystem.current.currentSelectedGameObject == null)
                {
                    HighlightFallbackSelectable();
                }
            }
        }


        // EVENT HANDLING

        private void Events_OnInputModeChanged()
        {
            // If using controller, make sure we have a selection
            if (_controls.UsingJoystick() && _currentSelection != null)
            {
                _currentSelection.Select();
            }
            else if (!_controls.UsingJoystick() && EventSystem.current.currentSelectedGameObject != null)
            {
                // Clear selection when using mouse
                EventSystem.current.SetSelectedGameObject(null);
            }
        }


        // ACCESSORS


        public void ResetProgress_ButtonClicked()
        {
            // Remember that the fresh start button was highlighted
            if (_controls.UsingJoystick() &&
                _freshStartButton != null &&
                _freshStartButton.gameObject.activeInHierarchy &&
                _freshStartButton.interactable)
            {
                // Make sure the fresh start button is highlighted
                _freshStartButton.Select();
            }

            // Set Cancel to null as we don't want to do anything on cancel
            _confirmationSubState.SetCallbacks(ConfirmationSubState_ResetTimesConfirmed, null);
            _menuController.EnterSubState(_confirmationSubState);
        }

        public void StartLevelButtonClicked()
        {
            //TODO
            _gameState.GameLevelDefinition = _gameLevelDefinition;
            int saveSlot = (int)GTWGameState.SaveSlotId.DOINKLER_SPECIAL;

            // bool hasSaveData = false;
            
            // if (_gameState.HasSaveData(saveSlot))
            // {
            //     hasSaveData = dataExists;
            // };
            _gameState.SetSaveSlot(saveSlot);
            
            _gameState.LoadSaveSlotMetaData(saveSlot, (gameStateData) =>
            {
                _gameData.HasSaveData(saveSlot, (dataExists) =>
                {
                    if (dataExists)
                    {
                        _gameState.LoadSaveGame(saveSlot);
                    }
                    else
                    {
                        // _gameState.SetSaveSlot(saveSlot);
                        _gameState.StartGameMode(_gameLevelDefinition.gameMode);
                    }
                });
            });

            // if (!hasSaveData)
            // {
            //     _gameState.SetSaveSlot(saveSlot);
            //     _gameState.StartGameMode(_gameLevelDefinition.gameMode);
            // }
        }


        // OTHER METHODS

        private void HighlightFallbackSelectable()
        {
            throw new NotImplementedException();
        }

        private void SetupStateMachineController(MonoStateMachine controller)
        {
            if (controller is MonoPushdownStateMachine stateMachine)
            {
                _menuController = stateMachine;
            }
            else
            {
                Debug.LogError("Using level select menu without a push down menu controller, this won't work");
            }
        }

        private void SetupProgressReport()
        {
            // TODO REPLACE THIS WITH REAL CODE
            _employeeNameText.text = "theDoinkler";
            _totalTimeText.text = "51 hr, 2 min, 4 sec";
            _currentCheckpointText.text = "5/45";
        }

        private void SetupCompanyLeaderboard()
        {
            // TODO - Grab steam leaderboard
        }




        private void ConfirmationSubState_ResetTimesConfirmed()
        {

        }



        private IEnumerator SetupControllerNavigation()
        {
            // Wait a frame to ensure all game level displays are properly set up
            yield return null;

            // TODO - add this


        }

    }
}