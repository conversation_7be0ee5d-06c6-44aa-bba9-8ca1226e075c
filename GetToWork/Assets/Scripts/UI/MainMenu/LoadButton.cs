// Copyright Isto Inc.
using Isto.Core.Data;
using Isto.Core.Speedrun;
using Isto.GTW;
using Isto.GTW.Providers;
using UnityEngine;
using Zenject;

namespace Code.Scripts.UI.MainMenu
{
    public class LoadButton : MonoBehaviour
    {
        // UNITY HOOKUP

        [Header("Speedrun Settings for Main Gameplay")]
        [SerializeField] SpeedrunSettings _mainGameSpeedrunSettings;


        // OTHER FIELDS

        private GTWGameState _gameState;
        private IGameData _gameData;


        // INJECTION

        [Inject]
        public void Inject(GTWGameState gameState, IGameData gameData)
        {
            _gameState = gameState;
            _gameData = gameData;
        }


        // OTHER METHODS

        public void LoadFirstSlot()
        {
            // Set the game level definition to null as the main game does not have it
            _gameState.GameLevelDefinition = null;
            RebindSpeedrunSettings();
            int saveSlot = (int) GTWGameState.SaveSlotId.MAIN_GAME;
            _gameState.LoadSaveSlotMetaData(saveSlot, (gameStateData) =>
            {
                _gameData.HasSaveData(saveSlot, (dataExists) =>
                {
                    if (dataExists)
                    {
                        _gameState.LoadSaveGame(saveSlot);
                    }
                });
            });
        }

        private void RebindSpeedrunSettings()
        {
            DiContainer projectContainer = ProjectContext.Instance.Container;
            projectContainer.Rebind(typeof(SpeedrunSettings))
                .FromInstance(_mainGameSpeedrunSettings)
                .AsCached()
                .NonLazy();
            projectContainer.Inject(_gameData);
        }
    }
}