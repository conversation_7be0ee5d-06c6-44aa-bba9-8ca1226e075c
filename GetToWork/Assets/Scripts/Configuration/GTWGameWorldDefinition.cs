// Copyright Isto Inc.

using I2.Loc;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.GTW.Configuration
{
    [CreateAssetMenu(fileName = "Scriptables/New GTW Game World Definition", menuName = "Scriptables/New GTW Game World Definition")]
    public class GTWGameWorldDefinition : ScriptableObject
    {
        public LocalizedString WorldTitle; // World title will show the user which world they are looking at (e.g. New York Stock Excchange)
        public List<GTWGameLevelDefinition> GameLevels; // These are all of the levels that the user can select for this world.
        public bool IsWorldCompleted => GameLevels.TrueForAll(l =>  PlayerPrefs.GetFloat(l.UniqueIDTotal, 0f) > 0f);
        public GTWGameState.SaveSlotId SaveSlotId;
    }
}