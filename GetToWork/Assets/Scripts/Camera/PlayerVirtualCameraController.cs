using System;
using Cinemachine;
using Isto.Core.Inputs;
using Isto.GTW.Player;
using Isto.GTW.Player.States;
using Isto.GTW.UI;
using PixelCrushers;
using UnityEditor;
using UnityEngine;
using Zenject;

namespace Isto.GTW.Camera
{
    /// <summary>
    ///
    /// </summary>
    [DefaultExecutionOrder(110)]
    public class PlayerVirtualCameraController : MonoBehaviour
    {
        // UNITY HOOKUP

        [SerializeField] private PlayerController _playerController = null;
        [SerializeField] private CinemachineVirtualCamera _virtualCamera = null;

        // _aimTarget, _followCam, and _followPosition work together (and are nested within each other).
        // We manipulate aimTarget and _followPosition and then Cinemachine uses those values to handle how the camera
        // moves.
        [SerializeField] private Transform _aimTarget = null;

        // _followCam is the absolute position of where the camera should be
        [SerializeField] private Transform _followCam = null;

        // _followPosition was needed to fix a quirk in cinemachine.
        // For _followPosition, we reset the rotation every frame. This resets the up vector that that cinemachine uses
        // for its "Hard Look At" feature (CinemachineVirtualCamera -> Aim -> Hard Look At).
        // Without resetting the up vector every frame, the "Hard Look At" would add a tilt on the camera, making it
        // more nauseating (and looked weird)
        [SerializeField] private Transform _followPosition = null;

        [Header("Settings")]
        [SerializeField] private PlayerVirtualCameraPreset _playerVirtualCameraPreset = null;

        // TEMP - For debug info  =========
        public Transform FollowPosition { get { return _followPosition; } }
        public CinemachineVirtualCamera CVC { get { return _virtualCamera; } }
        // ==========

        // OTHER FIELDS

        /// <summary>
        /// Because the player is a ball, the players speed never actually reaches 0.
        /// We consider the player to be stopped when speed is between 0 and 0.001f.
        /// </summary>
        private const float PLAYER_STOPPED_SPEED = 0.001f;
        private const float PLAYER_STOPPED_SPEED_PRECISE = 0.0001f;

        // These keys are used to save gameplay settings set by the user.

        private static string CAMERA_SENSITIVITY_PLAYER_PREFS_KEY => GTWUISettingsGameplaySubState.CAMERA_SENSITIVITY_PLAYER_PREFS_KEY;
        private static string CAMERA_INVERT_HORIZONTAL_PREFS_KEY => GTWUISettingsGameplaySubState.CAMERA_INVERT_HORIZONTAL_PREFS_KEY;
        private static string CAMERA_INVERT_VERTICAL_PREFS_KEY => GTWUISettingsGameplaySubState.CAMERA_INVERT_VERTICAL_PREFS_KEY;

        private static string CAMERA_HORIZONTAL_AUTO_ROTATION_PLAYER_PREFS_KEY = GTWUISettingsGameplaySubState.CAMERA_HORIZONTAL_AUTO_ROTATION_PLAYER_PREFS_KEY;
        private static string CAMERA_VERTICAL_AUTO_ROTATION_PLAYER_PREFS_KEY = GTWUISettingsGameplaySubState.CAMERA_VERTICAL_AUTO_ROTATION_PLAYER_PREFS_KEY;
        public static readonly string OCCLUSION_CULLING_DISABLED_EDITOR_PREF_KEY = "OcclusionCullingDisabled";

        private CustomCinemachineCollider _cinemachineCollider = null;
        private CinemachineTransposer _transposer = null;

        // The input of the right joystick (that controls the camera).
        private Vector2 _cameraInput = Vector2.zero;
        // The input of the left joystick (that controls player movement) without any modification.
        // Details on how we modify the raw input are in PlayerController.
        private Vector3 _rawMovementInput = Vector3.zero;
        private bool _hasControllerInput = false;
        private bool _hasMouseInput = false;

        // An offset on the root of the camera rig that moves the aim target and all of its children.
        // Currently only used for the edge grab, but could be used in any situation.
        private Vector3 _cameraPositionOffset = Vector3.zero;
        // Used to smoothly transition the camera from its default position to its offset.
        private Vector3 _cameraPositionOffsetVelocity = Vector3.zero;
        
        // An offset on the root added as extra effect. Used for Respawn fade in animation.
        private Vector3 _animatedCameraPositionOffset = Vector3.zero;

        // Gets the damping value on the VirtualCamera at the start of the game.
        // We turn on and off the damping during gameplay, and these save those values so they arent lost.
        // We turn off damping when:
        //      a) manually rotating the camera
        //      b) grabbing
        //      c) when the player is moving towards the camera.
        private Vector3 _defaultCameraPositionDamping = Vector3.zero;
        private Vector3 _defaultCameraRotationDamping = Vector3.zero;
        private Vector3 _defaultMouseCameraPositionDamping = Vector3.zero;

        private Vector3 _currentCameraPositionDamping = Vector3.zero;
        private Vector3 _currentCameraRotationDamping = Vector3.zero;


        // 3 values from 0 to 1. Used to turn on and off the damping (from its default damping value).
        // 0 is off, 1 is on.
        private Vector3 _dampingMultiplier = Vector3.one;
        // Stores the velocity for the Vector3.SmoothDamp().
        private Vector3 _dampingMultiplierVel = Vector3.one;

        // We work in euler units, x = vertical (pitch), y= horizontal (yaw), z = tilt.
        // we change the values individually in the code,
        // and it's much easier to do so in Euler (as opposed to quaternions)
        private Vector3 _camRotationEuler = Vector3.zero;
        private float _camVerticalTarget = 0f;
        private float _camVerticalVelocity = 0f;
        private Quaternion _tiltRotation = Quaternion.identity;


        private float _rotationInputBlockTimeLeft = -1f;
        private float _autoRotateResetTimeLeft = -1f;

        private float _reactionSpeed = 0f;
        private float _reactionSpeedVel = 0f;
        private Vector3 _cameraPlaneUp = Vector3.up;
        private Vector3 _cameraPlaneUpVel = Vector3.zero;
        private float _autoRotatedWeight = 1f;
        private float _autoRotateWeightVel = 0f;
        private float _currentVerticalWeight = 0f;
        private float _verticalWeightVel = 0f;

        private float _timeScaleFovIncrement = 0f;
        private float _timeScaleFovVelocity = 0f;

        // Overrides the camera parameters. Can be set from a PlayerCameraOverrideTrigger.
        private PlayerCameraOverride _playerCameraOverride = null;
        private PlayerCameraOverride.CameraOverride _cameraOverride = null;

        private float _currentCameraDistance = -1f;

        private LayerMask _defaultCameraColliderLayerMask;


        // PROPERTIES

        public bool UsingController => _controls.UsingJoystick();

        public Transform AimTarget => _aimTarget;
        public Transform FollowCam => _followCam;

        public Vector2 CameraInput => _cameraInput;
        public bool HasControllerInput => _hasControllerInput;
        public bool HasMouseInput => _hasMouseInput;


        // Note: All of the "magic numbers" (e.g. 200f) are the same default value as PlayerVirtualCameraPreset.cs. This just in case the camera preset is null.
        // You could technically remove all of them, but it acts as a back up.
        public float InputRotateSpeedMultiplier => _playerVirtualCameraPreset != null ? _playerVirtualCameraPreset.inputRotateSpeedMultiplier : 200f;
        public float MouseSensitivity => _playerVirtualCameraPreset != null ? _playerVirtualCameraPreset.mouseSensitivity : 0.1f;

        public bool AutoRotateEnabled => (_playerVirtualCameraPreset != null ? _playerVirtualCameraPreset.autoRotateEnabled : true);
        public bool AutoRotateEnabledDuringGrab => (_playerVirtualCameraPreset != null ? _playerVirtualCameraPreset.autoRotateEnabledDuringGrab : true);
        public float BaseAutoRotateGroundSpeed => _playerVirtualCameraPreset != null ? _playerVirtualCameraPreset.baseAutoRotateGroundSpeed : 3f;
        public float BaseAutoRotateAirSpeed => _playerVirtualCameraPreset != null ? _playerVirtualCameraPreset.baseAutoRotateAirSpeed : 3f;
        public AnimationCurve VerticalSpeedWeightCurve => _playerVirtualCameraPreset != null ? _playerVirtualCameraPreset.verticalSpeedWeightCurve : AnimationCurve.Linear(0f, 1f, 1f, 1f);
        public AnimationCurve AutoRotatePlayerSpeedCurve => _playerVirtualCameraPreset != null ? _playerVirtualCameraPreset.autoRotatePlayerSpeedCurve : AnimationCurve.Linear(0f, 1f, 1f, 1f);
        public AnimationCurve AutoRotateDampingPlayerSpeedCurve => _playerVirtualCameraPreset != null ? _playerVirtualCameraPreset.autoRotateDampingPlayerSpeedCurve : AnimationCurve.Linear(0f, 1f, 1f, 1f);
        public float DampingVerticalReturnThreshold => _playerVirtualCameraPreset != null ? _playerVirtualCameraPreset.dampingVerticalReturnThreshold : 10f;

        public float TimeToTurnDampingBackOn => _playerVirtualCameraPreset != null ? _playerVirtualCameraPreset.timeToTurnDampingBackOn : 0.1f;
        public float MouseAutoRotateResetDelay => _playerVirtualCameraPreset != null ? _playerVirtualCameraPreset.mouseAutoRotateResetDelay : 1f;
        public float ControllerAutoRotateResetDelay => _playerVirtualCameraPreset != null ? _playerVirtualCameraPreset.controllerAutoRotateResetDelay : 1f;
        public float AutoRotateEnableTransitionTime => _playerVirtualCameraPreset != null ? _playerVirtualCameraPreset.autoRotateEnableTransitionTime : 0.5f;

        public float MinVerticalAngleLimit => _playerVirtualCameraPreset != null ? _playerVirtualCameraPreset.minVerticalAngleLimit : -85f;
        public float MaxVerticalAngleLimit => _playerVirtualCameraPreset != null ? _playerVirtualCameraPreset.maxVerticalAngleLimit : 85f;
        public float Fov => _playerVirtualCameraPreset != null ? _playerVirtualCameraPreset.fov : 60f;

        //Slow time ability has been deprecated. It has potential to be fun, so we've left in case we want to add it back in.
        public float SlowTimeFovIncrement => 20f;

        public float Distance => _playerVirtualCameraPreset != null ? _playerVirtualCameraPreset.distance : 1f;
        public float AimTargetHeight => _playerVirtualCameraPreset != null ? _playerVirtualCameraPreset.aimTargetHeight : 0.5f;
        public float FollowCamHeight => _playerVirtualCameraPreset != null ? _playerVirtualCameraPreset.followCamHeight : 0.5f;
        public float HorizontalTiltAngle => _playerVirtualCameraPreset != null ? _playerVirtualCameraPreset.horizontaltiltAngle : 15f;
        public float VerticalTiltAngle => _playerVirtualCameraPreset != null ? _playerVirtualCameraPreset.verticaltiltAngle : 15f;
        public float TiltResponsiveness => _playerVirtualCameraPreset != null ? _playerVirtualCameraPreset.tiltResponsiveness : 1f;

        public float EdgeGrabCameraOffset => _playerVirtualCameraPreset != null ? _playerVirtualCameraPreset.edgeGrabCameraOffset : 0.1f;
        public float TimeToReachEdgeGrabOffset => _playerVirtualCameraPreset != null ? _playerVirtualCameraPreset.timeToReachEdgeGrabOffset : 0.5f;


        public Vector3 AnimatedCameraPositionOffset
        {
            get => _animatedCameraPositionOffset;
            set => _animatedCameraPositionOffset = value;
        }


        public PlayerCameraOverride PlayerCameraOverride => _playerCameraOverride;


        // PROPERTIES - Gameplay Settings

        public float CameraSensitivitySetting => PlayerPrefs.GetFloat(CAMERA_SENSITIVITY_PLAYER_PREFS_KEY, defaultValue: GTWUISettingsGameplaySubState.DEFAULT_CAMERA_SENSITIVITY);

        public bool CameraInvertHorizontal => PlayerPrefs.GetInt(CAMERA_INVERT_HORIZONTAL_PREFS_KEY, GTWUISettingsGameplaySubState.DEFAULT_CAMERA_INVERT_HORIZONTAL) == 1;

        public bool CameraInvertVertical => PlayerPrefs.GetInt(CAMERA_INVERT_VERTICAL_PREFS_KEY, GTWUISettingsGameplaySubState.DEFAULT_CAMERA_INVERT_VERTICAL) == 1;

        /// <summary>
        /// Adjusts how fast the camera autorotate works (or if it runs at all).
        ///
        /// 0f means the autorotate is off.
        /// Acts as a Big multiplier at the end of the camera speed calculations.
        /// </summary>
        public float CameraHorizontalAutoRotationSpeedSetting
        {
            get
            {
                // There are no booleans in PlayerPrefs, so we use 1 as true, 0 as false.
                bool autoRotationEnabled = PlayerPrefs.GetInt(CAMERA_HORIZONTAL_AUTO_ROTATION_PLAYER_PREFS_KEY, GTWUISettingsGameplaySubState.DEFAULT_CAMERA_HORIZONTAL_AUTO_ROTATION) == 1;

                // Note: All of the "magic numbers" (e.g. 200f) are the same default value as PlayerVirtualCameraPreset.cs. This just in case the camera preset is null.
                // You could technically remove all of them, but it acts as a back up.
                float defaultSetting = _playerVirtualCameraPreset != null ? _playerVirtualCameraPreset.horizontalAutoRotateSpeedMultiplier : 0.5f;
                return autoRotationEnabled ? defaultSetting : 0f;
            }
        }

        /// <summary>
        /// Adjusts how fast the camera autorotate works (or if it runs at all).
        ///
        /// 0f means the autorotate is off.
        /// Acts as a Big multiplier at the end of the camera speed calculations.
        /// </summary>
        public float CameraVerticalAutoRotationSpeedSetting
        {
            get
            {
                // There are no booleans in PlayerPrefs, so we use 1 as true, 0 as false.
                bool autoRotationEnabled = PlayerPrefs.GetInt(CAMERA_VERTICAL_AUTO_ROTATION_PLAYER_PREFS_KEY, GTWUISettingsGameplaySubState.DEFAULT_CAMERA_VERTICAL_AUTO_ROTATION) == 1;

                // Note: All of the "magic numbers" (e.g. 200f) are the same default value as PlayerVirtualCameraPreset.cs. This just in case the camera preset is null.
                // You could technically remove all of them, but it acts as a back up.
                float defaultSetting = _playerVirtualCameraPreset != null ? _playerVirtualCameraPreset.verticalAutoRotateSpeedMultiplier : 0.5f;
                return autoRotationEnabled ? defaultSetting : 0f;
            }
        }


        // INJECTION

        protected IControls _controls;
        protected CinemachineBrain _cinemachineBrain;

        [Inject]
        private void Inject(IControls controls, CinemachineBrain cinemachineBrain)
        {
            _controls = controls;
            _cinemachineBrain = cinemachineBrain;
        }


        // UNITY LIFECYCLE

        private void Awake()
        {
            _playerCameraOverride = GetComponent<PlayerCameraOverride>();
            _cinemachineCollider = _virtualCamera.GetComponent<CustomCinemachineCollider>();
            _defaultCameraColliderLayerMask = _cinemachineCollider.m_CollideAgainst;

            CinemachineComponentBase componentBase = _virtualCamera.GetCinemachineComponent(CinemachineCore.Stage.Body);
            _transposer = componentBase as CinemachineTransposer;

            _defaultCameraPositionDamping.x = _transposer.m_XDamping;
            _defaultCameraPositionDamping.y = _transposer.m_YDamping;
            _defaultCameraPositionDamping.z = _transposer.m_ZDamping;
            _defaultCameraRotationDamping.x = _transposer.m_PitchDamping;
            _defaultCameraRotationDamping.y = _transposer.m_YawDamping;
            _defaultCameraRotationDamping.z = _transposer.m_RollDamping;

            _defaultMouseCameraPositionDamping = new Vector3(0.1f, 0.1f, _defaultCameraPositionDamping.z);
        }

        private void Start()
        {
            _camRotationEuler = _aimTarget.rotation.eulerAngles;
        }

        private void OnDestroy()
        {
            CursorControl.SetCursorActive(true);
        }

        private void Update()
        {
            _cameraOverride = _playerCameraOverride.GetOverride();

            _cameraInput = GetCameraRotationInput();
            _rawMovementInput = _playerController.RawInput;

            UpdateCamera();

            UpdateOcclusionCulling();
        }

        private void LateUpdate()
        {
            UpdateCameraPosition();

            LateUpdateVirtualCameraParameters();

            if (_controls.GetControlMode() == Controls.Mode.Gameplay)
            {
                CursorControl.SetCursorActive(false);
            }
            else
            {
                CursorControl.SetCursorActive(!_controls.UsingJoystick());
            }
        }


        // OTHER METHODS

        private void SetCameraTransposerDamping(Vector3 positionDamping, Vector3 rotationDamping)
        {
            _currentCameraPositionDamping = positionDamping;
            _currentCameraRotationDamping = rotationDamping;

            _transposer.m_XDamping = positionDamping.x;
            _transposer.m_YDamping = positionDamping.y;
            _transposer.m_ZDamping = positionDamping.z;
            _transposer.m_PitchDamping = rotationDamping.x;
            _transposer.m_YawDamping = rotationDamping.y;
            _transposer.m_RollDamping = rotationDamping.z;
        }

        private void UpdateCameraTransposerDampingMultiplier(float multiplier)
        {
            UpdateCameraTransposerDampingMultiplier(new Vector3(multiplier, multiplier, multiplier));
        }

        private void UpdateCameraTransposerDampingMultiplier(Vector3 multiplier)
        {
            _transposer.m_XDamping = _currentCameraPositionDamping.x * multiplier.x;
            _transposer.m_YDamping = _currentCameraPositionDamping.y * multiplier.y;
            _transposer.m_ZDamping = _currentCameraPositionDamping.z * multiplier.z;
            _transposer.m_PitchDamping = _currentCameraRotationDamping.x * multiplier.x;
            _transposer.m_YawDamping = _currentCameraRotationDamping.y * multiplier.y;
            _transposer.m_RollDamping = _currentCameraRotationDamping.z * multiplier.z;
        }

        public void SetCameraDamping(Vector3 positionDamping)
        {
            SetCameraDamping(positionDamping, _defaultCameraRotationDamping);
        }

        public void SetCameraDamping(Vector3 positionDamping, Vector3 rotationDamping)
        {
            SetCameraTransposerDamping(positionDamping, rotationDamping);
        }

        public void SetDefaultCameraDamping(bool hasMouseInput = false)
        {
            if (_playerController.CurrentState.GetType() == typeof(GTWPlayerNoClipState))
            {
                SetCameraDamping(Vector3.zero, Vector3.zero);
            }
            else if (hasMouseInput)
            {
                SetCameraDamping(_defaultMouseCameraPositionDamping, _defaultCameraRotationDamping);
            }
            else
            {
                SetCameraDamping(_defaultCameraPositionDamping, _defaultCameraRotationDamping);
            }
        }

        /// <summary>
        /// A value between 0 and 1 where 1 = 100% weight.
        /// </summary>
        private float _ragdollPositionTargetWeight = 0f;

        /// <summary>
        /// Instantly sets the camera rotation (ignores all damping)
        /// </summary>
        /// <param name="rotationEuler">Vector2(Vertical rotation, Horizontal rotation)</param>
        public void SetCameraRotation(Vector2 rotationEuler)
        {
            _camRotationEuler = rotationEuler;
            _camVerticalTarget = rotationEuler.x;
            _camVerticalVelocity = 0f;

            float minVerticalAngle = _cameraOverride != null && _cameraOverride.hasMinVerticalAngleOverride ? _cameraOverride.minVerticalAngleOverride : MinVerticalAngleLimit;
            float maxVerticalAngle = _cameraOverride != null && _cameraOverride.hasMaxVerticalAngleOverride ? _cameraOverride.maxVerticalAngleOverride : MaxVerticalAngleLimit;
            _camRotationEuler.x = Mathf.Clamp(_camRotationEuler.x, minVerticalAngle, maxVerticalAngle);

            _aimTarget.rotation = Quaternion.Euler(_camRotationEuler);
        }

        /// <param name="instant">if true, instantly "snaps" camera to its target position (no smoothing). Useful when teleporting the character</param>
        public void UpdateCameraPosition(bool instant = false)
        {
            Vector3 positionTarget = transform.position;

            if (_playerController.PlayerModelController.PlayerIkController.PlayerRagdoll.IsRagdollEnabled)
            {
                // When in ragdoll, the camera needs to follows the characters body (instead of the player physics)
                Vector3 target = _playerController.PlayerModelController.PlayerIkController.PlayerRagdoll.RagdollBallTarget.position;

                if (_playerController.IsGrabLinkActive)
                {
                    // While hanging, we opted to not follow the pelvis as a target because the camera became too erratic.
                    target = _playerController.GrabTargetPosition;

                    Vector3 input = _playerController.MovementInput;

                    //Sets a camera offset based on movement inputs (left joystick) for feel.
                    _cameraPositionOffset = Vector3.SmoothDamp(
                        _cameraPositionOffset,
                        input * EdgeGrabCameraOffset,
                        ref _cameraPositionOffsetVelocity,
                        TimeToReachEdgeGrabOffset,
                        float.MaxValue,
                        Time.deltaTime);
                }

                if (instant)
                {
                    positionTarget = target;
                    _ragdollPositionTargetWeight = 1f;
                }
                else
                {
                    // Smoothly transition from the player to the ragdoll body
                    float distance = Vector3.Distance(transform.position, target);

                    // 5f = 5s. Length of the camera anchor transition from the playerPhysics to the ragdoll's body
                    _ragdollPositionTargetWeight = Mathf.MoveTowards(_ragdollPositionTargetWeight, 1f, Time.deltaTime * 5f);

                    if (_ragdollPositionTargetWeight >= 0.999f)
                    {
                        positionTarget = target;
                    }
                    else
                    {
                        // Adds a small lag to the camera at the start of the transition when in ragdoll mode.
                        //Prevents the camera from snapping when in ragdoll.
                        // The camera will speed up to normal near the end of the transition
                        //
                        // 20f = 20 m/s
                        // 1000f = 1000m/s
                        // Both are magic numbers based on feel.
                        positionTarget = Vector3.MoveTowards(transform.position, target, Time.deltaTime * distance * Mathf.Lerp(20f, 1000f, _ragdollPositionTargetWeight));
                    }
                }
            }
            else
            {
                //Smoothly sets the camera offset back to zero when out of ragdoll mode.
                _cameraPositionOffset = Vector3.SmoothDamp(
                    _cameraPositionOffset,
                    Vector3.zero,
                    ref _cameraPositionOffsetVelocity,
                    TimeToReachEdgeGrabOffset,
                    float.MaxValue,
                    Time.deltaTime);

                positionTarget = _playerController.PlayerModelController.transform.position;
                _ragdollPositionTargetWeight = 0f;
            }

            if (_cameraOverride != null && _cameraOverride.hasPositionOverride)
            {
                positionTarget = _cameraOverride.positionRotationOverride.position;
            }

            transform.position = positionTarget + _cameraPositionOffset + _animatedCameraPositionOffset;

            _followPosition.rotation = Quaternion.identity;
        }

        public void UpdateCamera(bool instantUpdate = false)
        {
            UpdateVirtualCameraParameters();

            Vector2 cameraInput = _cameraInput;

            if (instantUpdate)
            {
                cameraInput = Vector2.zero;
                _rawMovementInput = Vector3.zero;
            }

            if (_rotationInputBlockTimeLeft > 0f)
            {
                cameraInput = Vector2.zero;
            }

            _rotationInputBlockTimeLeft -= Time.deltaTime;

            if (instantUpdate)
            {
                UpdateCameraPosition(true);
            }

            bool autoRotate = AutoRotateEnabled && (_autoRotateResetTimeLeft < 0f);

            if (!AutoRotateEnabledDuringGrab && _playerController.IsGrabLinkActive)
            {
                autoRotate = false;
            }
            
            if (_playerController.TimeSinceLastMoving > 0.5f)
            {
                autoRotate = false;
            }

            if (_cinemachineBrain.ActiveVirtualCamera != _virtualCamera)
            {
                autoRotate = false;
                cameraInput = Vector2.zero;
            }

            if (autoRotate)
            {
                float fullWeight = 1f;//Weight between 0f and 1f, 1f is 100%
                _autoRotatedWeight = Mathf.SmoothDamp(_autoRotatedWeight, fullWeight, ref _autoRotateWeightVel, AutoRotateEnableTransitionTime, float.MaxValue, Time.deltaTime);
            }
            else
            {
                _autoRotateWeightVel = 0f;
                _autoRotatedWeight = 0f;
            }

            Vector3 playerDirectionFlat = _playerController.DirectionFlat;

            if (_playerController.IsGrabLinkActive && _playerController.Speed > PLAYER_STOPPED_SPEED)
            {
                //Use the velocity if it is longer than PLAYER_STOPPED_SPEED.
                playerDirectionFlat = _playerController.Velocity;
            }

            playerDirectionFlat.Normalize();
            Vector3 playerDirection = _playerController.Direction;
            Quaternion playerRotation = Quaternion.LookRotation(playerDirectionFlat, Vector3.up);

            float speedKph = _playerController.CurrentSpeedKPH;
            float baseAutoRotateSpeedSetting = (_playerController.PlayerCollisionHandler.IsGrounded && !_playerController.IsGrabLinkActive) ? BaseAutoRotateGroundSpeed : BaseAutoRotateAirSpeed;
            float baseAutoRotateSpeedTarget = _cameraOverride != null && _cameraOverride.hasReactionSpeedOverride ? _cameraOverride.reactionSpeedOverride : baseAutoRotateSpeedSetting;


            _reactionSpeed = Mathf.SmoothDamp(_reactionSpeed, baseAutoRotateSpeedTarget, ref _reactionSpeedVel, 1f, float.MaxValue, Time.deltaTime);

            //Speed up auto rotate and high speed.
            float baseAutoRotateSpeed = _reactionSpeed * AutoRotatePlayerSpeedCurve.Evaluate(speedKph);

            //Slow down auto rotate speed when the player is going straight up.
            //This prevents the camera from flipping when going up a wall on top of a quarter pipe.
            baseAutoRotateSpeed *= (1f - Mathf.InverseLerp(0.9f, 0.95f, Mathf.Abs(playerDirection.y)));

            //Half vertical speed when using a controller. Based on feel.
            float verticalSpeed = UsingController ? 0.5f : 1f;

            //Manual rotation with right joystick or mouse.
            _camRotationEuler.y += cameraInput.x * InputRotateSpeedMultiplier * Time.deltaTime;
            _camRotationEuler.x += -cameraInput.y * InputRotateSpeedMultiplier * Time.deltaTime * verticalSpeed;

            //Clamp vertical rotation while taking notice of the maximum additive tilt angle possible.
            _camRotationEuler.x = Mathf.Clamp(_camRotationEuler.x, MinVerticalAngleLimit + VerticalTiltAngle, MaxVerticalAngleLimit - VerticalTiltAngle);

            if (instantUpdate)
            {
                _autoRotateWeightVel = 0f;
                _reactionSpeedVel = 0f;
            }

            bool isNoClipControls = _playerController.CurrentState.GetType() == typeof(GTWPlayerNoClipState);

            if ((_autoRotatedWeight > 0f || instantUpdate) && !isNoClipControls)
            {
                //Horizontal rotation

                float horizontalTarget = playerRotation.eulerAngles.y;

                if (_playerController.IsGrabLinkActive && _playerController.IsGrabRagDoll)
                {
                    Transform grabParent = _playerController.PlayerIkController.PlayerRagdoll.GrabParent;
                    if (grabParent != null)
                    {
                        //Use the grabbed surface normal orientation when grabbing a moving object.
                        Vector3 grabFixedJointForward = -_playerController.GrabLinkNormal;
                        // DebugExtensions.DrawArrow(fixedJoint.transform.position, fixedJoint.transform.position + grabFixedJointForward, Color.magenta);
                        horizontalTarget = Vector3.SignedAngle(Vector3.forward, grabFixedJointForward, Vector3.up);
                    }
                }

                if (_cameraOverride != null && _cameraOverride.hasRotationOverride)
                {
                    horizontalTarget = _cameraOverride.positionRotationOverride.rotation.eulerAngles.y;
                }

                float deltaAngle = Mathf.Abs(Mathf.DeltaAngle(_camRotationEuler.y, horizontalTarget));

                if (instantUpdate)
                {
                    _camRotationEuler.y = horizontalTarget;
                }
                else
                {
                    _camRotationEuler.y = Mathf.MoveTowardsAngle(
                        _camRotationEuler.y,
                        horizontalTarget,
                        Time.deltaTime * baseAutoRotateSpeed * (deltaAngle / 90f) * CameraHorizontalAutoRotationSpeedSetting * _autoRotatedWeight);
                }


                //Vertical rotation

                float verticalTarget = 0f;

                verticalTarget = AutoRotateTargetAngleFromFloorOrientation(instantUpdate);

                // 2m/s and 10m/s are based on feel -> 10m/s is about 1/3 of your max speed
                float speedCoefficient = GetAutoRotateSpeedCoefficient(_playerController.Speed, 2f, 10f);

                // We adjust the vertical camera target and speed if the player is grabbing onto a ledge in a ragdoll state
                if (_playerController.IsGrabLinkActive && _playerController.IsGrabRagDoll)
                {
                    // When hanging (in ragdoll mode), set the camera took look slightly up.
                    // Helps avoid camera clipping into platforms (especially ceilings)
                    verticalTarget = -10f;
                    // based on feel -> a smaller speed that slows down the camera a little bit
                    speedCoefficient = 0.2f;
                }
                // We also adjust the camera when the player is in a ragdoll state while being still. This is separated
                // from the previous if statement to allow for control of the variables based on feel
                else if (_playerController.IsRagdollState && !_playerController.IsBodyMoving)
                {
                    // When hanging (in ragdoll mode), set the camera took look slightly up.
                    // Helps avoid camera clipping into platforms (especially ceilings)
                    verticalTarget = -10f;
                    // based on feel -> a smaller speed that slows down the camera a little bit
                    speedCoefficient = 0.4f;
                }
                
                if (_cameraOverride != null && _cameraOverride.hasRotationOverride)
                {
                    verticalTarget = _cameraOverride.positionRotationOverride.rotation.eulerAngles.x;
                }

                // Taking a multiplier value, and using that to determine a duration of time to go from the current value to _camVerticalTarget.
                // Controls the speed of the vertical rotation.
                float smoothTime = Mathf.Lerp(0.5f, 0.1f, CameraVerticalAutoRotationSpeedSetting);
                // The final target value of the camera vertical rotation
                _camVerticalTarget = Mathf.SmoothDamp(_camVerticalTarget, verticalTarget, ref _camVerticalVelocity, smoothTime, float.MaxValue, Time.deltaTime);

                if (instantUpdate)
                {
                    _camVerticalTarget = verticalTarget;
                    _camVerticalVelocity = 0f;
                    _camRotationEuler.x = _camVerticalTarget;
                }
                else
                {
                    // Note: CameraVerticalAutoRotationSpeedSetting acts as an on off switch
                    // _camRotationEuler is the actual position of the camera
                    // _camVerticalTarget is the ideal location
                    // We try to move towards the target in a smooth manner.


                    // The bigger the difference between actual and ideal position of camera, the faster the camera will rotate
                    // This is the multiplier that controls the speed of the rotation.
                    // Note: We use inverseLerp because it returns a value between 0 and 1 based on a range.
                    // For example, if the range is 0 degrees to DampingVerticalReturnThreshold=10 degrees,
                    // any value above 10 deg will return as 1, resulting in a maximum rotation speed.
                    // Note2: When inside the range, adds a damping to the camera to smooth it out.
                    float differenceWeight = Mathf.InverseLerp(0f, DampingVerticalReturnThreshold, Mathf.Abs(_camVerticalTarget - _camRotationEuler.x));

                    // Using the differenceWeight is a trick to simulate what SmoothDamp does.
                    // SmoothDamp expects you to reach your target, but MoveTowardsAngle is better
                    // suited to a value that's constantly moving.
                    float deltaTime = Time.deltaTime *
                                      differenceWeight *
                                      speedCoefficient * 180f *
                                      CameraVerticalAutoRotationSpeedSetting *
                                      _autoRotatedWeight;
                    _camRotationEuler.x = Mathf.MoveTowardsAngle(_camRotationEuler.x, _camVerticalTarget, deltaTime);
                }
            }

            // A final safety clamp so the camera doesn't go over the min/max camera angles (i.e. flip upside down).
            float minVerticalAngle = _cameraOverride != null && _cameraOverride.hasMinVerticalAngleOverride ? _cameraOverride.minVerticalAngleOverride : MinVerticalAngleLimit;
            float maxVerticalAngle = _cameraOverride != null && _cameraOverride.hasMaxVerticalAngleOverride ? _cameraOverride.maxVerticalAngleOverride : MaxVerticalAngleLimit;
            _camRotationEuler.x = Mathf.Clamp(_camRotationEuler.x, minVerticalAngle, maxVerticalAngle);

            Quaternion cameraRotation = Quaternion.Euler(_camRotationEuler);

            // Adds a tilting effect to the camera (has recently been removed, but code has been left in
            // in case we want to add it back in the future or want to simulate MonkeyBall)
            if (!isNoClipControls && UsingController)
            {
                _tiltRotation = Quaternion.RotateTowards(_tiltRotation,
                    Quaternion.Euler(_rawMovementInput.z * VerticalTiltAngle, 0f, _rawMovementInput.x * HorizontalTiltAngle),
                    Time.deltaTime * 360f * TiltResponsiveness);
                cameraRotation *= _tiltRotation;
            }

            _aimTarget.rotation = cameraRotation;

            if (instantUpdate)
            {
                // Force update CineMachine virtual camera.
                // Without this, we were experiencing some camera glitches
                // i.e. 1 bad frame where the CinemachineBrain wouldn't update in a frame.
                // Would sometime makes the player "dissappear" because the camera wasn't moving with the player.
                _virtualCamera.PreviousStateIsValid = false;
            }
        }

        /// <summary>
        /// Changes how fast the autorotate moves based on the players speed.
        /// Slowing down the autorotate at slower speeds helps users better finely control movement.
        /// </summary>
        /// <param name="playerSpeed">in m/s</param>
        /// <param name="slowestSpeed">Turns off the autorotate when the characters speed is moving slower than this speed (m/s)</param>
        /// <param name="maxSpeed">Lerps the speed of the autorotate to it's maximum speed when the character is going faster than this speed (m/s)</param>
        /// <returns></returns>
        private float GetAutoRotateSpeedCoefficient(float playerSpeed, float slowestSpeed, float maxSpeed)
        {
            return Mathf.Clamp01(Mathf.Max(0f, playerSpeed - slowestSpeed) / maxSpeed);
        }

        /// <summary>
        /// Adds a small adjustment to the vertical rotation depending on the floors orientation.
        /// This prevents camera clipping when going up/down hill.
        /// Using a Plane allows us to know if the player is going up/down or sideways on a slope.
        /// If you're moving horizontally on a slope (i.e not moving up or down), the camera won't move up or down
        /// </summary>
        /// <param name="instantUpdate"></param>
        /// <returns></returns>
        private float AutoRotateTargetAngleFromFloorOrientation(bool instantUpdate)
        {
            Vector3 velocity = _playerController.Velocity.normalized;

            if (instantUpdate && velocity.magnitude < PLAYER_STOPPED_SPEED_PRECISE)
            {
                velocity = _playerController.PlayerTransform.forward;
            }

            float verticalWeight = VerticalSpeedWeightCurve.Evaluate(_playerController.Velocity.y);
            _currentVerticalWeight = Mathf.SmoothDamp(_currentVerticalWeight, verticalWeight, ref _verticalWeightVel, 1f, float.MaxValue, Time.deltaTime);
            //Force the camera to default position based on vertical velocity
            velocity = Vector3.Lerp(_playerController.DirectionFlat, velocity, _currentVerticalWeight);


            Vector3 velocitySide = Vector3.Cross(velocity, Vector3.up);
            Vector3 velocityUp = Vector3.Cross(velocitySide, velocity);


            if (instantUpdate)
            {
                _cameraPlaneUp = _playerController.PlayerCollisionHandler.IsGrounded ? _playerController.PlayerCollisionHandler.FloorNormal : velocityUp;
                _cameraPlaneUpVel = Vector3.zero;
                _verticalWeightVel = 0f;
            }
            else
            {
                // Smooth the cameras movement so that the rotation is not instant
                // 0.2f = 0.2s. Magic number based on feel -> fairly quick but still smooth
                _cameraPlaneUp = Vector3.SmoothDamp(_cameraPlaneUp,
                    _playerController.PlayerCollisionHandler.IsGrounded ? _playerController.PlayerCollisionHandler.FloorNormal : velocityUp,
                    ref _cameraPlaneUpVel,
                    0.2f,
                    float.MaxValue, Time.deltaTime);
            }


            // toCameraOnVelocityPlane.y is a Normalized Vector from the center of the plane to the camera
            // (essentially a "back vector" from the player)
            // If we use the y value on the vector and multiply it by 45 degrees, it turns the y value into a range between -45 and 45 degrees.
            // This adds the rotation based on the slope.
            Plane velocityPlane = new Plane(_cameraPlaneUp, _playerController.PlayerPosition);
            Vector3 toCameraOnVelocityPlane = velocityPlane.ClosestPointOnPlane(_aimTarget.position - _aimTarget.forward) - _playerController.PlayerPosition;
            toCameraOnVelocityPlane.Normalize();
            return toCameraOnVelocityPlane.y * 45f;
        }

        private void UpdateVirtualCameraParameters()
        {
            //Field of view

            float timeScaleFovIncrementTarget = Mathf.Lerp(0f, SlowTimeFovIncrement, _playerController.TimeManager.SlowTimeWeight);
            _timeScaleFovIncrement = Mathf.SmoothDamp(_timeScaleFovIncrement,
                timeScaleFovIncrementTarget,
                ref _timeScaleFovVelocity,
                0.05f);

            _virtualCamera.m_Lens.FieldOfView = Fov + _timeScaleFovIncrement;


            //Follow camera distance and height

            float followCamHeight = _cameraOverride != null && _cameraOverride.hasFollowCamHeightOverride ? _cameraOverride.followCamHeightOverride : FollowCamHeight;
            Vector3 followCamPos = _followCam.localPosition;

            _currentCameraDistance = Mathf.MoveTowards(
                _currentCameraDistance,
                _cameraOverride != null && _cameraOverride.hasDistanceOverride ? -Mathf.Abs(_cameraOverride.distanceOverride) : -Distance,
                Time.deltaTime * 15f);

            followCamPos.z = _currentCameraDistance;
            followCamPos.y = followCamHeight;
            _followCam.localPosition = followCamPos;

            float aimTargetHeight = _cameraOverride != null && _cameraOverride.hasAimTargetHeightOverride ? _cameraOverride.aimTargetHeightOverride : AimTargetHeight;
            float aimTargetHeightTarget = aimTargetHeight;
            if (_playerController.PlayerModelController.PlayerIkController.PlayerRagdoll.IsRagdollEnabled)
            {
                aimTargetHeightTarget = 0f;
            }
            Vector3 aimTargetPosition = _aimTarget.localPosition;
            aimTargetPosition.y = Mathf.MoveTowards(aimTargetPosition.y, aimTargetHeightTarget, Time.deltaTime);

            _aimTarget.localPosition = aimTargetPosition;
            _followCam.LookAt(_aimTarget, _aimTarget.up);


            //Update virtual camera collision layers

            _cinemachineCollider.m_CollideAgainst = _cameraOverride != null && _cameraOverride.hasCollisionLayerOverride ? _cameraOverride.collisionLayerOverride : _defaultCameraColliderLayerMask;
        }

        private void LateUpdateVirtualCameraParameters()
        {
            // Virtual camera damping (camera lag)

            float speed = _playerController.CurrentSpeedKPH;
            if (_playerController.CurrentState.GetType() == typeof(GTWPlayerRagdollState))
            {
                //_playerController.CurrentSpeedKPH is not updated in ragdoll mode, use PelvisRigidBody velocity instead.
                speed = _playerController.PlayerIkController.PlayerRagdoll.PelvisRigidBody.linearVelocity.magnitude * 3.6f;
            }

            Vector3 dampingMultiplierTarget = Vector3.one;
            dampingMultiplierTarget.z = AutoRotateDampingPlayerSpeedCurve.Evaluate(speed);
            Vector3 toAimTarget = AimTarget.position - _followCam.position;
            Vector3 playerDirection = _playerController.Direction;
            float angleUp = Vector3.Angle(Vector3.up, toAimTarget);
            float angleDown = Vector3.Angle(Vector3.down, toAimTarget);
            float angleForward = Vector3.Angle(playerDirection, toAimTarget);
            toAimTarget.y = 0f;
            float angleForwardHorizontal = Vector3.Angle(playerDirection, toAimTarget);
            float maxAngle = MathF.Max(MathF.Max(angleUp, angleDown), angleForwardHorizontal);

            dampingMultiplierTarget.x *= Mathf.InverseLerp(170f, 130f, maxAngle);//Turn off damping when looking down or up to avoid camera stutter
            dampingMultiplierTarget.y *= Mathf.InverseLerp(170f, 130f, maxAngle);//Turn off damping when looking down or up to avoid camera stutter
            dampingMultiplierTarget.z *= Mathf.InverseLerp(170f, 130f, angleForward);//Turn off damping when moving toward the camera to avoid camera stutter

            bool isGrabbing = _playerController.IsGrabLinkActive && _playerController.GrabbingStickySurface;
            bool isCamInput = _autoRotateResetTimeLeft > 0f;// CameraInput.sqrMagnitude > 0f;
            dampingMultiplierTarget.x *= (isGrabbing || isCamInput) ? 0f : 1f;
            dampingMultiplierTarget.y *= (isGrabbing || isCamInput) ? 0f : 1f;
            dampingMultiplierTarget.z *= (isGrabbing) ? 0f : 1f;

            _dampingMultiplier = Vector3.SmoothDamp(_dampingMultiplier,
                dampingMultiplierTarget,
                ref _dampingMultiplierVel,
                isCamInput ? 0.1f : TimeToTurnDampingBackOn,
                float.MaxValue,
                Time.deltaTime);

            UpdateCameraTransposerDampingMultiplier(_dampingMultiplier);
        }

        private Vector2 GetCameraRotationInput()
        {
            Vector2 input = Vector2.zero;

            input.x = _controls.GetAxis(Controls.MovementAxis.SecondaryHorizontal);
            input.y = _controls.GetAxis(Controls.MovementAxis.SecondaryVertical);

            //0.05 dead zone
            if (Mathf.Abs(input.x) < 0.05f)
            {
                input.x = 0f;
            }
            if (Mathf.Abs(input.y) < 0.05f)
            {
                input.y = 0f;
            }

            // We use tiny value 0.000001f to account for stick dift.
            // Use use sqrMagnitude for performance reasons (saves one square root calculation)
            _hasControllerInput = input.sqrMagnitude > 0.000001f;
            _hasMouseInput = false;

            if (Time.deltaTime > 0f)
            {
                //TODO: proper mouse control through rewired
                Vector2 mouseDelta = new Vector2(Input.GetAxis("Mouse X"), Input.GetAxis("Mouse Y")) / Time.deltaTime;
                input += mouseDelta * MouseSensitivity;

                // We use tiny value 0.000001f to account for mouse drift.
                _hasMouseInput = mouseDelta.sqrMagnitude > 0.000001f;

                if (_hasMouseInput)
                {
                    _hasControllerInput = false;
                }
            }

            if (_hasMouseInput)
            {
                SetAutoRotateResetTime(MouseAutoRotateResetDelay);
            }
            else if (_hasControllerInput)
            {
                SetAutoRotateResetTime(ControllerAutoRotateResetDelay);
            }
            else
            {
                if (_autoRotateResetTimeLeft < 0f)
                {
                    SetDefaultCameraDamping(false);
                }
            }

            if (!_hasMouseInput)
            {
                // If you are tilting your joystick far to the Right, Left, Up or Down,
                // it cancels out the other rotations.
                // I.e. if the player is tilting to the right, its clear they only want to head right, and not up and down.
                // This feels intuitive to the player.

                // Doing this essentially converts the "circle" of your joystick into a "diamond",
                if (Mathf.Abs(input.x) > Mathf.Abs(input.y))
                {
                    input.y *= 1f - Mathf.Abs(input.x) * 0.5f;
                }
                else
                {
                    input.x *= 1f - Mathf.Abs(input.y) * 0.5f;
                }
            }

            _autoRotateResetTimeLeft -= Time.deltaTime;

            float sensitivitySetting = CameraSensitivitySetting;
            sensitivitySetting *= 2f;

            input.x *= CameraInvertHorizontal ? -1f : 1f;
            input.y *= CameraInvertVertical ? -1f : 1f;


            return input * sensitivitySetting;
        }

        public void SetAutoRotateResetTime(float time)
        {
            if (_autoRotateResetTimeLeft < time)
            {
                _autoRotateResetTimeLeft = time;
            }
        }

        public void SetRotationInputBlockTime(float time)
        {
            if (_rotationInputBlockTimeLeft < time)
            {
                _rotationInputBlockTimeLeft = time;
            }
        }

        private void UpdateOcclusionCulling()
        {
#if UNITY_EDITOR
            bool occlusionCullingEnabled = !EditorPrefs.GetBool(OCCLUSION_CULLING_DISABLED_EDITOR_PREF_KEY, false);
            UnityEngine.Camera.main.useOcclusionCulling = occlusionCullingEnabled;
#endif
        }
    }
}